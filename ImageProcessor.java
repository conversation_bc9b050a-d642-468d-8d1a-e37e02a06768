import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

/**
 * 图片像素增强和像素连接处理器
 * 功能：将图片中的灰色或浅黑色标记增强为纯黑色，背景转换为纯白色，并连接断开的黑色线条
 * 与Python版本保持完全一致
 */
public class ImageProcessor {
    
    private static final int DEFAULT_THRESHOLD = 220;
    private static final boolean DEFAULT_CONNECT = true;
    
    public static void main(String[] args) {
        // 解析命令行参数
        String inputPath = null;
        String outputPath = null;
        int threshold = DEFAULT_THRESHOLD;
        boolean connect = DEFAULT_CONNECT;
        
        for (int i = 0; i < args.length; i++) {
            switch (args[i]) {
                case "--input":
                    if (i + 1 < args.length) inputPath = args[++i];
                    break;
                case "--output":
                    if (i + 1 < args.length) outputPath = args[++i];
                    break;
                case "--threshold":
                    if (i + 1 < args.length) threshold = Integer.parseInt(args[++i]);
                    break;
                case "--connect":
                    if (i + 1 < args.length) connect = Boolean.parseBoolean(args[++i]);
                    break;
                case "--help":
                    printUsage();
                    return;
            }
        }
        
        // 检查必要参数
        if (inputPath == null || outputPath == null) {
            System.err.println("错误：缺少必要参数");
            printUsage();
            System.exit(1);
        }
        
        try {
            // 处理图片
            processImage(inputPath, outputPath, threshold, connect);
            System.out.println("图片处理成功：" + outputPath);
        } catch (Exception e) {
            System.err.println("图片处理失败：" + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }
    
    /**
     * 处理图片
     */
    public static void processImage(String inputPath, String outputPath, int threshold, boolean connect) throws IOException {
        // 读取图片
        BufferedImage inputImage = ImageIO.read(new File(inputPath));
        if (inputImage == null) {
            throw new IOException("无法读取输入图片：" + inputPath);
        }
        
        // 转换为灰度图 - 与Python的image.convert('L')保持一致
        BufferedImage grayscaleImage = convertToGrayscale(inputImage);
        
        // 应用阈值处理 - 与Python的grayscale_image.point(lambda p: 0 if p < threshold else 255)完全一致
        BufferedImage thresholdImage = applyThreshold(grayscaleImage, threshold);
        
        // 应用像素连接（如果需要）并转换为RGB格式
        BufferedImage finalImage;
        if (connect) {
            finalImage = applyPixelConnection(thresholdImage);
        } else {
            // 不使用像素连接时，直接转换灰度图为RGB格式，与Python的threshold_image.convert('RGB')一致
            finalImage = convertGrayscaleToRGB(thresholdImage);
        }
        
        // 保存结果 - 强制输出PNG格式以确保无损保存
        // 自动将输出文件扩展名改为.png
        String pngOutputPath = outputPath;
        if (!outputPath.toLowerCase().endsWith(".png")) {
            // 移除原有扩展名并添加.png
            int lastDotIndex = outputPath.lastIndexOf('.');
            if (lastDotIndex > 0) {
                pngOutputPath = outputPath.substring(0, lastDotIndex) + ".png";
            } else {
                pngOutputPath = outputPath + ".png";
            }
            System.out.println("注意：输出格式已自动更改为PNG格式：" + pngOutputPath);
        }
        
        File outputFile = new File(pngOutputPath);
        if (outputFile.getParentFile() != null) {
            outputFile.getParentFile().mkdirs(); // 确保输出目录存在
        }
        
        // 强制使用PNG格式输出，确保无损保存
        if (!ImageIO.write(finalImage, "png", outputFile)) {
            throw new IOException("无法保存输出图片：" + pngOutputPath);
        }
    }
    

    
    /**
     * 转换为灰度图 - 与Python的image.convert('L')完全一致
     * 使用ITU-R 601-2亮度公式：Y = 0.299*R + 0.587*G + 0.114*B
     */
    private static BufferedImage convertToGrayscale(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();
        
        BufferedImage grayscale = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_GRAY);
        
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int rgb = image.getRGB(x, y);
                
                // 提取RGB分量
                int r = (rgb >> 16) & 0xFF;
                int g = (rgb >> 8) & 0xFF;
                int b = rgb & 0xFF;
                
                // 使用ITU-R 601-2亮度公式，与PIL的convert('L')一致
                int gray = (int) (0.299 * r + 0.587 * g + 0.114 * b + 0.5);
                
                // 确保在有效范围内
                gray = Math.min(255, Math.max(0, gray));
                
                // 设置灰度值
                int grayRgb = (gray << 16) | (gray << 8) | gray;
                grayscale.setRGB(x, y, grayRgb);
            }
        }
        
        return grayscale;
    }
    
    /**
     * 应用阈值处理 - 与Python完全一致
     */
    private static BufferedImage applyThreshold(BufferedImage image, int threshold) {
        int width = image.getWidth();
        int height = image.getHeight();
        
        // 保持灰度格式，与Python的threshold_image保持一致
        BufferedImage result = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_GRAY);
        
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int rgb = image.getRGB(x, y);
                int gray = rgb & 0xFF; // 获取灰度值
                
                // 应用阈值：与Python的lambda p: 0 if p < threshold else 255完全一致
                int newGray = (gray < threshold) ? 0 : 255;
                
                // 设置灰度值
                int newRgb = (newGray << 16) | (newGray << 8) | newGray;
                result.setRGB(x, y, newRgb);
            }
        }
        
        return result;
    }
    
    /**
     * 应用像素连接 - 与Python版本完全一致
     */
    private static BufferedImage applyPixelConnection(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();
        
        // 转换为数组以便直接修改（与Python版本保持一致）
        int[][] pixelArray = new int[height][width];
        
        // 初始化像素数组 - 从灰度图像中直接获取像素值，与Python的arr = np.array(threshold_image)一致
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int rgb = image.getRGB(x, y);
                pixelArray[y][x] = rgb & 0xFF; // 直接获取灰度值（对于TYPE_BYTE_GRAY，RGB分量相等）
            }
        }
        
        // 横向扫描，相差两个像素进行连接（与Python版本完全一致）
        for (int y = 0; y < height; y++) {
            for (int x = 2; x < width - 2; x++) {
                if (pixelArray[y][x - 2] == 0 && pixelArray[y][x - 1] == 255 && 
                    pixelArray[y][x] == 255 && pixelArray[y][x + 1] == 255 && 
                    pixelArray[y][x + 2] == 0) {
                    // 直接在数组上修改，与Python版本保持一致
                    pixelArray[y][x - 1] = 0;
                    pixelArray[y][x] = 0;
                    pixelArray[y][x + 1] = 0;
                }
            }
        }
        
        // 纵向扫描，相差两个像素进行连接（与Python版本完全一致）
        for (int x = 0; x < width; x++) {
            for (int y = 2; y < height - 2; y++) {
                if (pixelArray[y - 2][x] == 0 && pixelArray[y - 1][x] == 255 && 
                    pixelArray[y][x] == 255 && pixelArray[y + 1][x] == 255 && 
                    pixelArray[y + 2][x] == 0) {
                    // 直接在数组上修改，与Python版本保持一致
                    pixelArray[y - 1][x] = 0;
                    pixelArray[y][x] = 0;
                    pixelArray[y + 1][x] = 0;
                }
            }
        }
        
        // 将修改后的数组转换回BufferedImage - 使用RGB格式与Python保持一致
        BufferedImage result = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int gray = pixelArray[y][x];
                // 与Python的Image.fromarray(arr).convert('RGB')保持一致
                int rgb = (gray << 16) | (gray << 8) | gray;
                result.setRGB(x, y, rgb);
            }
        }
        
        return result;
    }
    
    /**
     * 将灰度图像转换为RGB格式 - 与Python的threshold_image.convert('RGB')一致
     */
    private static BufferedImage convertGrayscaleToRGB(BufferedImage grayscaleImage) {
        int width = grayscaleImage.getWidth();
        int height = grayscaleImage.getHeight();
        
        BufferedImage rgbImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int rgb = grayscaleImage.getRGB(x, y);
                // 获取灰度值（对于TYPE_BYTE_GRAY图像，RGB分量都相等）
                int gray = rgb & 0xFF;
                
                // 构造RGB值，每个分量都是相同的灰度值
                int newRgb = (gray << 16) | (gray << 8) | gray;
                rgbImage.setRGB(x, y, newRgb);
            }
        }
        
        return rgbImage;
    }
    
    /**
     * 打印使用说明
     */
    private static void printUsage() {
        System.out.println("图片像素增强和像素连接处理器");
        System.out.println("用法：java ImageProcessor [选项]");
        System.out.println("选项：");
        System.out.println("  --input <文件路径>     输入图片文件路径");
        System.out.println("  --output <文件路径>    输出图片文件路径");
        System.out.println("  --threshold <数值>     阈值（默认：200）");
        System.out.println("  --connect <true|false> 是否使用像素连接（默认：true）");
        System.out.println("  --help                显示此帮助信息");
        System.out.println();
        System.out.println("示例：");
        System.out.println("  java ImageProcessor --input input.jpg --output output.jpg --threshold 200 --connect true");
    }
}
